@include('layout.admin-header')

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Billing History</h1>
                        <div>
                            <a href="{{ route('admin.subscription.current') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Subscription
                            </a>
                            <a href="{{ route('admin.subscription.plans') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                                <i class="fas fa-eye fa-sm text-white-50"></i> View Plans
                            </a>
                        </div>
                    </div>

                    <!-- Billing Summary Cards -->
                    <div class="row">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Total Invoices</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $invoices->total() }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Paid Invoices</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ $invoices->where('status', 'paid')->count() }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Pending Invoices</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ $invoices->where('status', 'pending')->count() }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Total Amount</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                ${{ number_format($invoices->sum('total_amount'), 2) }}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Invoices Table -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Invoice History</h6>
                        </div>
                        <div class="card-body">
                            @if($invoices->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>Invoice #</th>
                                                <th>Date</th>
                                                <th>Plan</th>
                                                <th>Billing Cycle</th>
                                                <th>Subtotal</th>
                                                <th>Tax</th>
                                                <th>Total</th>
                                                <th>Status</th>
                                                <th>Due Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($invoices as $invoice)
                                            <tr>
                                                <td>
                                                    <strong>#{{ $invoice->id }}</strong>
                                                </td>
                                                <td>{{ $invoice->invoice_date->format('M d, Y') }}</td>
                                                <td>
                                                    @if($invoice->subscription && $invoice->subscription->plan)
                                                        {{ $invoice->subscription->plan->name }}
                                                    @else
                                                        <span class="text-muted">N/A</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($invoice->subscription)
                                                        <span class="badge badge-secondary">
                                                            {{ ucfirst($invoice->subscription->billing_cycle) }}
                                                        </span>
                                                    @else
                                                        <span class="text-muted">N/A</span>
                                                    @endif
                                                </td>
                                                <td>${{ number_format($invoice->subtotal, 2) }}</td>
                                                <td>${{ number_format($invoice->tax_amount, 2) }}</td>
                                                <td>
                                                    <strong>${{ number_format($invoice->total_amount, 2) }}</strong>
                                                </td>
                                                <td>
                                                    @if($invoice->status === 'paid')
                                                        <span class="badge badge-success">Paid</span>
                                                    @elseif($invoice->status === 'pending')
                                                        <span class="badge badge-warning">Pending</span>
                                                    @elseif($invoice->status === 'overdue')
                                                        <span class="badge badge-danger">Overdue</span>
                                                    @elseif($invoice->status === 'cancelled')
                                                        <span class="badge badge-secondary">Cancelled</span>
                                                    @else
                                                        <span class="badge badge-light">{{ ucfirst($invoice->status) }}</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    {{ $invoice->due_date->format('M d, Y') }}
                                                    @if($invoice->status !== 'paid' && $invoice->due_date->isPast())
                                                        <br><small class="text-danger">
                                                            <i class="fas fa-exclamation-triangle"></i> Overdue
                                                        </small>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ route('admin.subscription.invoice.download', $invoice) }}" 
                                                           class="btn btn-sm btn-primary" title="Download PDF">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-info" 
                                                                data-toggle="modal" data-target="#invoiceModal{{ $invoice->id }}" 
                                                                title="View Details">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-center">
                                    {{ $invoices->links() }}
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-file-invoice fa-3x text-gray-300 mb-4"></i>
                                    <h4 class="text-gray-600">No Invoices Found</h4>
                                    <p class="text-muted">You don't have any billing history yet.</p>
                                    <a href="{{ route('admin.subscription.plans') }}" class="btn btn-primary">
                                        <i class="fas fa-eye"></i> View Subscription Plans
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Invoice Detail Modals -->
    @foreach($invoices as $invoice)
    <div class="modal fade" id="invoiceModal{{ $invoice->id }}" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Invoice #{{ $invoice->id }} Details</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="font-weight-bold">Invoice Information</h6>
                            <p><strong>Invoice Date:</strong> {{ $invoice->invoice_date->format('M d, Y') }}</p>
                            <p><strong>Due Date:</strong> {{ $invoice->due_date->format('M d, Y') }}</p>
                            <p><strong>Status:</strong> 
                                @if($invoice->status === 'paid')
                                    <span class="badge badge-success">Paid</span>
                                @elseif($invoice->status === 'pending')
                                    <span class="badge badge-warning">Pending</span>
                                @elseif($invoice->status === 'overdue')
                                    <span class="badge badge-danger">Overdue</span>
                                @else
                                    <span class="badge badge-secondary">{{ ucfirst($invoice->status) }}</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="font-weight-bold">Billing Information</h6>
                            <p><strong>Hotel:</strong> {{ $currentHotel->name }}</p>
                            @if($invoice->subscription && $invoice->subscription->plan)
                                <p><strong>Plan:</strong> {{ $invoice->subscription->plan->name }}</p>
                                <p><strong>Billing Cycle:</strong> {{ ucfirst($invoice->subscription->billing_cycle) }}</p>
                            @endif
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6 class="font-weight-bold">Line Items</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($invoice->line_items as $item)
                                <tr>
                                    <td>{{ $item['description'] }}</td>
                                    <td>{{ $item['quantity'] }}</td>
                                    <td>${{ number_format($item['unit_price'], 2) }}</td>
                                    <td>${{ number_format($item['total'], 2) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Subtotal</th>
                                    <th>${{ number_format($invoice->subtotal, 2) }}</th>
                                </tr>
                                <tr>
                                    <th colspan="3">Tax</th>
                                    <th>${{ number_format($invoice->tax_amount, 2) }}</th>
                                </tr>
                                <tr class="table-primary">
                                    <th colspan="3">Total</th>
                                    <th>${{ number_format($invoice->total_amount, 2) }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="{{ route('admin.subscription.invoice.download', $invoice) }}" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download PDF
                    </a>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    @endforeach

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>
</body>
