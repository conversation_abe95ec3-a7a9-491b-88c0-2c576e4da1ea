@include('layout.admin-header')

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        @include('layout.admin-sidebar')
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                @include('layout.admin-topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    @include('shared.success-message')
                    @include('shared.error-message')

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Subscription Plans</h1>
                        <a href="{{ route('admin.subscription.current') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Current Plan
                        </a>
                    </div>

                    <!-- Current Plan Info -->
                    @if($currentHotel && $currentHotel->currentSubscription)
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Current Plan</h6>
                        <p class="mb-0">
                            You are currently on the <strong>{{ $currentHotel->currentSubscription->plan->name }}</strong> plan 
                            ({{ ucfirst($currentHotel->currentSubscription->billing_cycle) }})
                            @if($currentHotel->currentSubscription->onTrial())
                                - <span class="badge badge-warning">Trial ({{ $currentHotel->currentSubscription->daysLeftOnTrial() }} days left)</span>
                            @endif
                        </p>
                    </div>
                    @endif

                    <!-- Pricing Plans -->
                    <div class="row">
                        @foreach($plans as $plan)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 {{ $plan->is_popular ? 'border-primary' : '' }}">
                                @if($plan->is_popular)
                                <div class="card-header bg-primary text-white text-center">
                                    <i class="fas fa-star"></i> Most Popular
                                </div>
                                @endif
                                
                                <div class="card-body text-center">
                                    <h4 class="card-title">{{ $plan->name }}</h4>
                                    <p class="text-muted">{{ $plan->description }}</p>
                                    
                                    <div class="pricing mb-3">
                                        <h2 class="text-primary">{{ $plan->formatted_price }}</h2>
                                        <small class="text-muted">per {{ $plan->billing_cycle }}</small>
                                    </div>

                                    <ul class="list-unstyled">
                                        @foreach($plan->features as $feature)
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success"></i> {{ $feature }}
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                                
                                <div class="card-footer">
                                    @if($currentHotel && $currentHotel->currentSubscription && $currentHotel->currentSubscription->plan->id === $plan->id)
                                        <button class="btn btn-secondary btn-block" disabled>
                                            <i class="fas fa-check"></i> Current Plan
                                        </button>
                                    @else
                                        @if($plan->canAccommodateHotel($currentHotel))
                                            <a href="{{ route('admin.subscription.upgrade', $plan) }}" class="btn btn-primary btn-block">
                                                <i class="fas fa-arrow-up"></i> 
                                                @if($currentHotel && $currentHotel->currentSubscription)
                                                    Upgrade to {{ $plan->name }}
                                                @else
                                                    Choose {{ $plan->name }}
                                                @endif
                                            </a>
                                        @else
                                            <button class="btn btn-warning btn-block" disabled title="This plan cannot accommodate your current setup">
                                                <i class="fas fa-exclamation-triangle"></i> Not Compatible
                                            </button>
                                            <small class="text-muted d-block mt-2">
                                                Your hotel has {{ $currentHotel->rooms()->count() }} rooms and {{ $currentHotel->users()->count() }} users.
                                                This plan supports up to {{ $plan->max_rooms ?: 'unlimited' }} rooms and {{ $plan->max_users ?: 'unlimited' }} users.
                                            </small>
                                        @endif
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Features Comparison -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Feature Comparison</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Feature</th>
                                            @foreach($plans as $plan)
                                            <th class="text-center">{{ $plan->name }}</th>
                                            @endforeach
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Rooms</strong></td>
                                            @foreach($plans as $plan)
                                            <td class="text-center">{{ $plan->max_rooms ?: 'Unlimited' }}</td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td><strong>Users</strong></td>
                                            @foreach($plans as $plan)
                                            <td class="text-center">{{ $plan->max_users ?: 'Unlimited' }}</td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td><strong>Online Booking</strong></td>
                                            @foreach($plans as $plan)
                                            <td class="text-center">
                                                @if($plan->hasFeature('Online booking'))
                                                    <i class="fas fa-check text-success"></i>
                                                @else
                                                    <i class="fas fa-times text-danger"></i>
                                                @endif
                                            </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td><strong>Restaurant Management</strong></td>
                                            @foreach($plans as $plan)
                                            <td class="text-center">
                                                @if($plan->hasFeature('Restaurant management'))
                                                    <i class="fas fa-check text-success"></i>
                                                @else
                                                    <i class="fas fa-times text-danger"></i>
                                                @endif
                                            </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td><strong>Custom Branding</strong></td>
                                            @foreach($plans as $plan)
                                            <td class="text-center">
                                                @if($plan->hasFeature('Custom branding'))
                                                    <i class="fas fa-check text-success"></i>
                                                @else
                                                    <i class="fas fa-times text-danger"></i>
                                                @endif
                                            </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td><strong>API Access</strong></td>
                                            @foreach($plans as $plan)
                                            <td class="text-center">
                                                @if($plan->hasFeature('API access'))
                                                    <i class="fas fa-check text-success"></i>
                                                @else
                                                    <i class="fas fa-times text-danger"></i>
                                                @endif
                                            </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td><strong>Support</strong></td>
                                            @foreach($plans as $plan)
                                            <td class="text-center">
                                                @if($plan->hasFeature('24/7 phone support'))
                                                    24/7 Phone
                                                @elseif($plan->hasFeature('Priority support'))
                                                    Priority Email
                                                @else
                                                    Email
                                                @endif
                                            </td>
                                            @endforeach
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Section -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Frequently Asked Questions</h6>
                        </div>
                        <div class="card-body">
                            <div class="accordion" id="faqAccordion">
                                <div class="card">
                                    <div class="card-header" id="faq1">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse1">
                                                Can I change my plan anytime?
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="collapse1" class="collapse" data-parent="#faqAccordion">
                                        <div class="card-body">
                                            Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and billing is prorated.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card">
                                    <div class="card-header" id="faq2">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse2">
                                                What happens if I exceed my plan limits?
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="collapse2" class="collapse" data-parent="#faqAccordion">
                                        <div class="card-body">
                                            If you exceed your plan limits, you'll be prompted to upgrade to a higher plan. We'll help you migrate seamlessly.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card">
                                    <div class="card-header" id="faq3">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse3">
                                                Is there a setup fee?
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="collapse3" class="collapse" data-parent="#faqAccordion">
                                        <div class="card-body">
                                            No, there are no setup fees. You only pay the monthly or yearly subscription fee for your chosen plan.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            @include('layout.admin-footer')
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>
</body>
