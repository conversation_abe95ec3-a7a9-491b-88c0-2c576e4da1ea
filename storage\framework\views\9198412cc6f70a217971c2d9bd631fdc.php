<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php
$currencySymbol = config('currency.symbol');
?>
<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            
                            <?php
                            $lowStockIngredients = $ingredients_list->filter(function($ingredient) {
                                return $ingredient->stock < $ingredient->pending_usage;
                            });
                            ?>
                            <?php if($lowStockIngredients->count() > 0): ?>
                                <div class="alert alert-warning" role="alert">
                                    <h4 class="alert-heading">Alert</h4>
                                    <p>The following items have current stock less than pending usage for upcoming events/catering</p>
                                    
                                    <ul>
                                        <?php $__currentLoopData = $lowStockIngredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($ingredient->ingredient_name); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            <h6 class="m-0 font-weight-bold text-primary">Items</h6>
                            <a href="<?php echo e(route('admin.ingredient.add')); ?>" class="btn btn-primary float-right"><i class="fa fa-plus"></i> Add Item</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Item Name</th>
                                            <th>Supplier</th>
                                            <th>Cost</th>
                                            <th>Min Qty</th>
                                            <th>Current Stock</th>
                                            <th>Pending Usage</th>
                                            <th>(Deficit)/Surplus</th>
                                            <th>Action</th>
                                            
                                            
                                        </tr>
                                    </thead>
                                    <!-- Moment -->
                                    <script src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>
                                    <tbody>
                                        <?php $__currentLoopData = $ingredients_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $buffer = $ingredient->stock - $ingredient->minqty;
                                                $pending = $ingredient->pending_usage > $ingredient->stock? true: false;
                                                $needed = -$ingredient->pending_usage + $ingredient->stock - $ingredient->minqty;
                                            ?>
                                            <tr>
                                                <td><?php echo e($ingredient->id); ?></td>
                                                <td><?php echo e($ingredient->ingredient_name); ?></td>
                                                <td><?php echo e($ingredient->supplier); ?></td>
                                                <td class="text-right"><?php echo e($currencySymbol); ?><?php echo e($ingredient->cost); ?></td>
                                                <td><?php echo e($ingredient->minqty.' '.$ingredient->unit); ?></td>
                                                <td <?php if ($buffer <=0) { echo 'class="table-danger"'; } ?>><?php echo e($ingredient->stock  .' '. $ingredient->unit); ?></td>
                                                <td <?php if ($pending==true) { echo 'class="table-warning"'; } ?>><?php echo e($ingredient->pending_usage .' '. $ingredient->unit); ?></td>
                                                <td><?php echo e($needed .' '. $ingredient->unit); ?></td>
                                                <td>
                                                    <a href="<?php echo e(route('admin.ingredient.edit', $ingredient->id)); ?>"
                                                        class="btn btn-primary btn-circle btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.ingredient.destroy', $ingredient->id)); ?>"
                                                        method="POST" style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-danger btn-circle btn-sm">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                               
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('layout.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views/admin/ingredients/ingredient-view.blade.php ENDPATH**/ ?>