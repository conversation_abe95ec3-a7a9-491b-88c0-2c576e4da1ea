<!-- Bootstrap core JavaScript-->
<script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>

<!-- Core plugin JavaScript-->
<script src="<?php echo e(asset('assets/js/jquery.easing.min.js')); ?>"></script>

<!-- Custom scripts for all pages-->
<script src="<?php echo e(asset('assets/js/sb-admin-2.min.js')); ?>"></script>

<!-- Page level plugins -->
<script src="<?php echo e(asset('assets/js/Chart.min.js')); ?>"></script>

<!-- Page level custom scripts -->
<script src="<?php echo e(asset('assets/js/chart-area-demo.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/chart-pie-demo.js')); ?>"></script>

<!-- DataTables CSS -->
<link rel="stylesheet" href="<?php echo e(asset('assets/css/dataTables.min.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('assets/css/dataTables.dataTables.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/css/buttons.dataTables.css')); ?>">
<!--Data Tables-->
<script src="<?php echo e(asset('assets/js/dataTables.js')); ?>"></script>

<script src="<?php echo e(asset('assets/js/dataTables.buttons.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/buttons.dataTables.js')); ?>"></script>


<!--Datetime Picker JS and CSS-->
<script src="<?php echo e(asset('assets/js/datetimepicker.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/datetimepicker.min.js')); ?>"></script>
<link href="<?php echo e(asset('assets/css/datetimepicker.min.css')); ?>" rel="stylesheet" />

<script src="<?php echo e(asset('assets/js/jquery-ui.js')); ?>"></script>
<link rel="stylesheet" href="<?php echo e(asset('assets/css/jquery-ui.css')); ?>">

<!-- Removed duplicate Bootstrap loading to prevent conflicts -->
<script src="<?php echo e(asset('assets/js/simple-datatables.min.js')); ?>" crossorigin="anonymous"></script>

<script src="<?php echo e(asset('assets/js/buttons.print.min.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/jszip.min.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/pdfmake.min.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/vfs_fonts.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/buttons.html5.min.js')); ?>"></script>

<script>
    $(document).ready(function() {
        // Initialize DataTable with ColResize for the kitchen table
        $('#myRequests').DataTable({
            scrollX: true, //vince added
            colReorder: true,
            colResize: {
                "tableWidthFixed": false
            },
            order: [[0, 'desc']], // Replace '0' with the index of the column you want to sort by default
            dom: 'Bfrtip', // Add this line to include buttons
            buttons: [
                'copy', 'excel', 'pdf', 'print'
            ]
        });
    });
</script>

<script>
    $(document).ready(function() {
        // Initialize DataTable with ColResize for the groceries table
        $('#myRequests2').DataTable({
            scrollX: true, //vince added
            colReorder: true,
            colResize: {
                "tableWidthFixed": false
            },
            order: [[0, 'desc']], // Replace '0' with the index of the column you want to sort by default
            dom: 'Bfrtip', // Add this line to include buttons
            buttons: [
                'copy', 'excel', 
                {   extend: 'pdfHtml5', //vince replaced 'pdf' with this block to align right col 4 in pdf
                    customize: function(doc) {
                    // Align the specific column in the PDF
                    if (doc.content && doc.content[1] && doc.content[1].table) {
                        doc.content[1].table.body.forEach(function(row, index) {
                            // Check if the column exists in the row
                            if (row[4] && index > 0) { // Change 4 to your column index
                                row[4].style = { alignment: 'right' }; // Right align for PDF
                            }
                        });
                    }
                }}, 'print'
            ],
            
            columnDefs: [ //vince added columnDefs, doesnt work for pdf though
                { targets: [ 4 ], className: 'dt-right' }
            ]
            
        });
    });
</script>





<!-- CSS For Select2 -->
<link href="<?php echo e(asset('assets/css/select2.min.css')); ?>" rel="stylesheet" />

<!-- JavaScript for Select2 -->
<script src="<?php echo e(asset('assets/js/select2.min.js')); ?>"></script>
<script>
    // Initialize Select2 for dropdowns
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>

<!-- JavaScript for General Button Confirmation (Buttons Should have class of ConfirmButton) -->
<script>
    // Select all elements with the class 'ConfirmButton'
    var ConfirmButton = document.querySelectorAll(".ConfirmButton");
    
    // Iterate over each delete button and attach event listener
    ConfirmButton.forEach(function(button) {
        button.addEventListener("click", function(event) {
            if (confirm("Confirm to Continue")) {
                // Find the closest form and submit it
                this.closest(".deleteForm").submit();
            } else {
                event.preventDefault(); // Prevent form submission
            }
        });
    });
</script>

<!-- JavaScript for Delete Button Confirmation (Buttons Should have class of deleteButton) -->
<script>
    // Select all elements with the class 'deleteButton'
    var deleteButtons = document.querySelectorAll(".deleteButton");

    // Iterate over each delete button and attach event listener
    deleteButtons.forEach(function(button) {
        button.addEventListener("click", function(event) {
            if (confirm("Are you sure you want to delete this document?")) {
                // Find the closest form and submit it
                this.closest(".deleteForm").submit();
            } else {
                event.preventDefault(); // Prevent form submission
            }
        });
    });
    //vince added block
    $(document).ready(function() {
        $('input[required]').each(function() {
            // Find the label associated with the required input
            var label = $("label[for='" + $(this).attr('id') + "']");

            // Append a red asterisk to the label if it doesn't already have one
            if (label.length && !label.find('.required-asterisk').length) {
                label.append('<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span>');
            }
        });

        // Initialize Bootstrap dropdowns and ensure they work properly
        $('.dropdown-toggle').dropdown();
    });
    //vince end
</script>


<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views/layout/scripts.blade.php ENDPATH**/ ?>